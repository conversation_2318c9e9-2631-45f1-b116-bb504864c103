package com.tourism.miniprogram.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.tourism.miniprogram.entity.ExplanationArea;

import java.util.List;

/**
 * 讲解区域服务接口
 *
 * <AUTHOR> Team
 * @since 2023-12-01
 */
public interface ExplanationAreaService extends IService<ExplanationArea> {

    /**
     * 根据产品ID获取区域列表
     *
     * @param productId 产品ID
     * @return 区域列表
     */
    List<ExplanationArea> getAreasByProductId(Integer productId);
}
