package com.tourism.miniprogram.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.tourism.miniprogram.entity.ExplanationArea;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 讲解区域Mapper接口
 *
 * <AUTHOR> Team
 * @since 2023-12-01
 */
@Mapper
public interface ExplanationAreaMapper extends BaseMapper<ExplanationArea> {

    /**
     * 根据产品ID获取区域列表
     *
     * @param productId 产品ID
     * @return 区域列表
     */
    List<ExplanationArea> selectAreasByProductId(@Param("productId") Integer productId);
}
