# 关系管理系统部署指南

## 系统要求

### 后端要求
- Java 1.8 或更高版本
- Maven 3.6+
- MySQL 8.0+
- Spring Boot 2.x

### 前端要求
- Node.js 16+
- npm 8+ 或 yarn 1.22+
- Vue 3
- Element Plus

## 部署步骤

### 1. 数据库准备

#### 1.1 创建数据库
```sql
CREATE DATABASE tourism_miniprogram CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

#### 1.2 执行表结构脚本
```bash
# 进入项目目录
cd backend/src/main/resources/sql

# 执行主数据库脚本
mysql -u root -p tourism_miniprogram < ../../../../../../tourism_miniprogram.sql

# 执行关系表初始化脚本
mysql -u root -p tourism_miniprogram < relation_tables.sql

# (可选) 执行测试数据脚本
mysql -u root -p tourism_miniprogram < test_data.sql
```

### 2. 后端部署

#### 2.1 配置数据库连接
编辑 `backend/src/main/resources/application.yml`:

```yaml
spring:
  datasource:
    url: *******************************************************************************************************************
    username: your_username
    password: your_password
    driver-class-name: com.mysql.cj.jdbc.Driver
```

#### 2.2 编译和启动后端
```bash
# 进入后端目录
cd backend

# 清理和编译
mvn clean compile

# 启动应用
mvn spring-boot:run

# 或者打包后运行
mvn clean package
java -jar target/miniprogram-backend-1.0.0.jar
```

#### 2.3 验证后端启动
访问 Swagger 文档：`http://localhost:8080/doc.html`

### 3. 前端部署

#### 3.1 安装依赖
```bash
# 进入前端目录
cd fronted

# 安装依赖
npm install
# 或使用 yarn
yarn install
```

#### 3.2 配置API地址
编辑 `fronted/src/utils/request.js`，确保baseURL指向正确的后端地址：

```javascript
const service = axios.create({
  baseURL: 'http://localhost:8080', // 后端API地址
  timeout: 10000
})
```

#### 3.3 启动前端开发服务器
```bash
# 开发模式启动
npm run dev
# 或使用 yarn
yarn dev
```

#### 3.4 构建生产版本
```bash
# 构建生产版本
npm run build
# 或使用 yarn
yarn build
```

### 4. 访问系统

#### 4.1 前端访问地址
- 开发环境：`http://localhost:3000`
- 生产环境：根据部署配置

#### 4.2 后端API地址
- Swagger文档：`http://localhost:8080/doc.html`
- 关系管理API：`http://localhost:8080/relations`

## 功能验证

### 1. 验证关系管理功能

#### 1.1 访问管理界面
1. 打开浏览器访问前端地址
2. 在侧边栏找到"关系管理"菜单
3. 点击进入关系管理页面

#### 1.2 测试产品-区域关系
1. 选择"产品-区域关系"标签页
2. 从下拉列表选择产品和区域
3. 点击"添加关系"按钮
4. 验证关系列表中显示新增的关系

#### 1.3 测试区域-讲解点关系
1. 选择"区域-讲解点关系"标签页
2. 选择区域查看关联的讲解点
3. 添加新的关系并验证

#### 1.4 测试讲解点-音频关系
1. 选择"讲解点-音频关系"标签页
2. 选择讲解点查看关联的音频
3. 测试音频播放功能

### 2. API测试

使用提供的 `api_test.http` 文件进行API测试：

```bash
# 使用 VS Code REST Client 插件
# 或者使用 curl 命令测试

# 测试获取区域列表
curl -X GET "http://localhost:8080/relations/areas"

# 测试添加关系
curl -X POST "http://localhost:8080/relations/product-area" \
  -H "Content-Type: application/json" \
  -d '{"productId": 1, "areaId": 2, "sortOrder": 1}'
```

## 故障排除

### 1. 常见问题

#### 1.1 数据库连接失败
- 检查数据库服务是否启动
- 验证连接参数是否正确
- 确认数据库用户权限

#### 1.2 前端API调用失败
- 检查后端服务是否启动
- 验证CORS配置
- 检查网络连接

#### 1.3 关系添加失败
- 检查外键约束
- 验证数据是否已存在
- 查看后端日志

### 2. 日志查看

#### 2.1 后端日志
```bash
# 查看应用日志
tail -f logs/application.log

# 查看错误日志
tail -f logs/error.log
```

#### 2.2 前端日志
打开浏览器开发者工具查看控制台输出

### 3. 性能优化

#### 3.1 数据库优化
- 确保关键字段有索引
- 定期清理无用数据
- 监控查询性能

#### 3.2 应用优化
- 调整JVM参数
- 配置连接池
- 启用缓存

## 生产环境部署

### 1. 后端生产部署

#### 1.1 使用Docker部署
```dockerfile
FROM openjdk:8-jre-alpine
COPY target/miniprogram-backend-1.0.0.jar app.jar
EXPOSE 8080
ENTRYPOINT ["java", "-jar", "/app.jar"]
```

#### 1.2 使用Nginx反向代理
```nginx
server {
    listen 80;
    server_name your-domain.com;
    
    location /api/ {
        proxy_pass http://localhost:8080/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
    
    location / {
        root /var/www/frontend;
        try_files $uri $uri/ /index.html;
    }
}
```

### 2. 前端生产部署

#### 2.1 构建静态文件
```bash
npm run build
```

#### 2.2 部署到Web服务器
```bash
# 复制构建文件到Web服务器
cp -r dist/* /var/www/frontend/
```

## 监控和维护

### 1. 系统监控
- 监控API响应时间
- 监控数据库连接数
- 监控错误率

### 2. 定期维护
- 备份数据库
- 清理日志文件
- 更新依赖包

## 联系支持

如遇到部署问题，请联系开发团队或查看项目文档。
