package com.tourism.miniprogram.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.tourism.miniprogram.entity.ExplanationAudio;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 讲解音频Mapper接口
 *
 * <AUTHOR> Team
 * @since 2023-12-01
 */
@Mapper
public interface ExplanationAudioMapper extends BaseMapper<ExplanationAudio> {

    /**
     * 根据讲解点ID获取音频列表
     *
     * @param pointId 讲解点ID
     * @return 音频列表
     */
    List<ExplanationAudio> selectAudiosByPointId(@Param("pointId") Integer pointId);
}
