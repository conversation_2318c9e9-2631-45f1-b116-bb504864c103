# 景区API接口文档

## 1. 根据景区ID获取景区详情

### 接口信息
- **URL**: `/api/scenics/{scenicId}`
- **方法**: GET
- **描述**: 根据景区ID获取景区详细信息

### 请求参数
| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| scenicId | String | 是 | 景区唯一标识 |

### 请求示例
```http
GET /api/scenics/scenic_1733515805317_abc123
```

### 响应示例
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "id": 1,
    "scenicId": "scenic_1733515805317_abc123",
    "title": "南京夫子庙",
    "subtitle": "历史文化街区",
    "description": "南京夫子庙是一组规模宏大的古建筑群...",
    "image": "http://example.com/cover.jpg",
    "images": "[\"http://example.com/img1.jpg\", \"http://example.com/img2.jpg\"]",
    "openTime": "08:00-22:00",
    "address": "南京市秦淮区夫子庙街道",
    "provinceId": 1,
    "cityId": 1,
    "sort": 1,
    "status": 1,
    "createdAt": "2023-12-01T10:00:00",
    "updatedAt": "2023-12-01T10:00:00"
  },
  "timestamp": 1701417600000
}
```

## 2. 根据省份ID获取景区列表

### 接口信息
- **URL**: `/api/scenics/province/{provinceId}`
- **方法**: GET
- **描述**: 获取指定省份下的所有启用景区

### 请求参数
| 参数名 | 类型 | 必填 | 描述 | 默认值 |
|--------|------|------|------|--------|
| provinceId | Integer | 是 | 省份ID | - |
| page | Long | 否 | 当前页 | 1 |
| limit | Long | 否 | 每页大小 | 10 |

### 请求示例
```http
GET /api/scenics/province/1?page=1&limit=10
```

### 响应示例
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "records": [
      {
        "id": 1,
        "scenicId": "scenic_1733515805317_abc123",
        "title": "南京夫子庙",
        "subtitle": "历史文化街区",
        "description": "南京夫子庙是一组规模宏大的古建筑群...",
        "image": "http://example.com/cover.jpg",
        "images": "[\"http://example.com/img1.jpg\", \"http://example.com/img2.jpg\"]",
        "openTime": "08:00-22:00",
        "address": "南京市秦淮区夫子庙街道",
        "provinceId": 1,
        "cityId": 1,
        "sort": 1,
        "status": 1,
        "createdAt": "2023-12-01T10:00:00",
        "updatedAt": "2023-12-01T10:00:00"
      }
    ],
    "total": 15,
    "size": 10,
    "current": 1,
    "pages": 2
  },
  "timestamp": 1701417600000
}
```

## 3. 根据城市ID获取景区列表

### 接口信息
- **URL**: `/api/scenics/city/{cityId}`
- **方法**: GET
- **描述**: 获取指定城市下的所有启用景区

### 请求参数
| 参数名 | 类型 | 必填 | 描述 | 默认值 |
|--------|------|------|------|--------|
| cityId | Integer | 是 | 城市ID | - |
| page | Long | 否 | 当前页 | 1 |
| limit | Long | 否 | 每页大小 | 10 |

### 请求示例
```http
GET /api/scenics/city/1?page=1&limit=5
```

### 响应示例
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "records": [
      {
        "id": 1,
        "scenicId": "scenic_1733515805317_abc123",
        "title": "南京夫子庙",
        "subtitle": "历史文化街区",
        "description": "南京夫子庙是一组规模宏大的古建筑群...",
        "image": "http://example.com/cover.jpg",
        "images": "[\"http://example.com/img1.jpg\", \"http://example.com/img2.jpg\"]",
        "openTime": "08:00-22:00",
        "address": "南京市秦淮区夫子庙街道",
        "provinceId": 1,
        "cityId": 1,
        "sort": 1,
        "status": 1,
        "createdAt": "2023-12-01T10:00:00",
        "updatedAt": "2023-12-01T10:00:00"
      }
    ],
    "total": 8,
    "size": 5,
    "current": 1,
    "pages": 2
  },
  "timestamp": 1701417600000
}
```

## 错误响应示例

### 景区不存在
```json
{
  "code": 404,
  "message": "景区不存在",
  "data": null,
  "timestamp": 1701417600000
}
```

### 参数错误
```json
{
  "code": 400,
  "message": "参数错误",
  "data": null,
  "timestamp": 1701417600000
}
```

### 服务器错误
```json
{
  "code": 500,
  "message": "获取景区列表失败",
  "data": null,
  "timestamp": 1701417600000
}
```

## 注意事项

1. **分页参数限制**：
   - `page` 最小值为 1
   - `limit` 范围为 1-100，超出范围会使用默认值 10

2. **状态过滤**：
   - 省份和城市景区列表API只返回 `status=1` 的启用景区
   - 景区详情API不过滤状态

3. **排序规则**：
   - 按 `sort` 字段升序排列
   - 相同 `sort` 值按 `id` 降序排列

4. **图片字段**：
   - `image`: 封面图片URL
   - `images`: JSON格式的图片数组字符串
