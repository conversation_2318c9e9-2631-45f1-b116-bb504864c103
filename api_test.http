### 关系管理系统 API 测试文件
### 使用 VS Code REST Client 插件或 IntelliJ HTTP Client 运行

@baseUrl = http://localhost:8080
@contentType = application/json

### ==================== 产品-区域关系管理 ====================

### 1. 获取产品的区域关系列表
GET {{baseUrl}}/relations/product-area/product/1
Accept: application/json

### 2. 获取区域的产品关系列表  
GET {{baseUrl}}/relations/area-point/area/1
Accept: application/json

### 3. 添加产品区域关系
POST {{baseUrl}}/relations/product-area
Content-Type: {{contentType}}

{
  "productId": 1,
  "areaId": 2,
  "sortOrder": 5
}

### 4. 删除产品区域关系
DELETE {{baseUrl}}/relations/product-area?productId=1&areaId=2

### 5. 批量添加产品区域关系
POST {{baseUrl}}/relations/product-area/batch
Content-Type: {{contentType}}

[
  {
    "productId": 2,
    "areaId": 1,
    "sortOrder": 1
  },
  {
    "productId": 2,
    "areaId": 3,
    "sortOrder": 2
  }
]

### 6. 更新产品区域关系排序
PUT {{baseUrl}}/relations/product-area/1/sort?sortOrder=10

### 7. 批量更新产品区域关系排序
PUT {{baseUrl}}/relations/product-area/batch-sort
Content-Type: {{contentType}}

{
  "relationIds": [1, 2, 3],
  "sortOrders": [1, 2, 3]
}

### ==================== 区域-讲解点关系管理 ====================

### 8. 获取区域的讲解点关系列表
GET {{baseUrl}}/relations/area-point/area/1
Accept: application/json

### 9. 获取讲解点的区域关系列表
GET {{baseUrl}}/relations/area-point/point/1
Accept: application/json

### 10. 添加区域讲解点关系
POST {{baseUrl}}/relations/area-point
Content-Type: {{contentType}}

{
  "areaId": 1,
  "pointId": 2,
  "sortOrder": 1
}

### 11. 删除区域讲解点关系
DELETE {{baseUrl}}/relations/area-point?areaId=1&pointId=2

### 12. 批量添加区域讲解点关系
POST {{baseUrl}}/relations/area-point/batch
Content-Type: {{contentType}}

[
  {
    "areaId": 2,
    "pointId": 1,
    "sortOrder": 1
  },
  {
    "areaId": 2,
    "pointId": 3,
    "sortOrder": 2
  }
]

### 13. 更新区域讲解点关系排序
PUT {{baseUrl}}/relations/area-point/1/sort?sortOrder=5

### ==================== 讲解点-音频关系管理 ====================

### 14. 获取讲解点的音频关系列表
GET {{baseUrl}}/relations/point-audio/point/1
Accept: application/json

### 15. 获取音频的讲解点关系列表
GET {{baseUrl}}/relations/point-audio/audio/1
Accept: application/json

### 16. 添加讲解点音频关系
POST {{baseUrl}}/relations/point-audio
Content-Type: {{contentType}}

{
  "pointId": 1,
  "audioId": 2,
  "sortOrder": 1
}

### 17. 删除讲解点音频关系
DELETE {{baseUrl}}/relations/point-audio?pointId=1&audioId=2

### 18. 批量添加讲解点音频关系
POST {{baseUrl}}/relations/point-audio/batch
Content-Type: {{contentType}}

[
  {
    "pointId": 2,
    "audioId": 1,
    "sortOrder": 1
  },
  {
    "pointId": 2,
    "audioId": 3,
    "sortOrder": 2
  }
]

### 19. 更新讲解点音频关系排序
PUT {{baseUrl}}/relations/point-audio/1/sort?sortOrder=3

### ==================== 辅助查询接口 ====================

### 20. 获取所有区域列表
GET {{baseUrl}}/relations/areas
Accept: application/json

### 21. 获取所有音频列表
GET {{baseUrl}}/relations/audios
Accept: application/json

### ==================== 错误测试 ====================

### 22. 测试无效参数 - 添加重复关系
POST {{baseUrl}}/relations/product-area
Content-Type: {{contentType}}

{
  "productId": 1,
  "areaId": 1,
  "sortOrder": 1
}

### 23. 测试无效参数 - 缺少必填字段
POST {{baseUrl}}/relations/area-point
Content-Type: {{contentType}}

{
  "areaId": 1,
  "sortOrder": 1
}

### 24. 测试无效参数 - 不存在的ID
GET {{baseUrl}}/relations/product-area/product/99999
Accept: application/json

### ==================== 性能测试 ====================

### 25. 批量操作性能测试
POST {{baseUrl}}/relations/point-audio/batch
Content-Type: {{contentType}}

[
  {"pointId": 1, "audioId": 1, "sortOrder": 1},
  {"pointId": 1, "audioId": 2, "sortOrder": 2},
  {"pointId": 1, "audioId": 3, "sortOrder": 3},
  {"pointId": 1, "audioId": 4, "sortOrder": 4},
  {"pointId": 1, "audioId": 5, "sortOrder": 5}
]
