com\tourism\miniprogram\dto\FileUploadResult$FileUploadResultBuilder.class
com\tourism\miniprogram\entity\ProductBundle.class
com\tourism\miniprogram\service\ReviewService.class
com\tourism\miniprogram\service\FileService.class
com\tourism\miniprogram\service\impl\CityServiceImpl.class
com\tourism\miniprogram\service\LecturerService.class
com\tourism\miniprogram\config\TencentCosConfig.class
com\tourism\miniprogram\service\UsageRecordService.class
com\tourism\miniprogram\service\ProductService.class
com\tourism\miniprogram\entity\ActivationCode.class
com\tourism\miniprogram\service\impl\ProductServiceImpl.class
com\tourism\miniprogram\entity\GuideProduct.class
com\tourism\miniprogram\config\WechatConfig.class
com\tourism\miniprogram\controller\ScenicController.class
com\tourism\miniprogram\service\CityService.class
com\tourism\miniprogram\service\ShouyeCarouselService.class
com\tourism\miniprogram\entity\City.class
com\tourism\miniprogram\mapper\CityMapper.class
com\tourism\miniprogram\mapper\GuideProductMapper.class
com\tourism\miniprogram\service\GuidePointService.class
com\tourism\miniprogram\service\impl\ScenicServiceImpl.class
com\tourism\miniprogram\mapper\ScenicMapper.class
com\tourism\miniprogram\dto\FileUploadResult.class
com\tourism\miniprogram\entity\Review.class
com\tourism\miniprogram\mapper\ProductBundleMapper.class
com\tourism\miniprogram\controller\CityController.class
com\tourism\miniprogram\entity\Coupon.class
com\tourism\miniprogram\mapper\ProductMapper.class
com\tourism\miniprogram\service\impl\FileServiceImpl.class
com\tourism\miniprogram\entity\Province.class
com\tourism\miniprogram\entity\Scenic.class
com\tourism\miniprogram\entity\ShouyeCarousel.class
com\tourism\miniprogram\service\ProvinceService.class
com\tourism\miniprogram\controller\UsageRecordController.class
com\tourism\miniprogram\controller\ReviewController.class
com\tourism\miniprogram\entity\Lecturer.class
com\tourism\miniprogram\entity\GuidePoint.class
com\tourism\miniprogram\common\Result.class
com\tourism\miniprogram\service\impl\ProvinceServiceImpl.class
com\tourism\miniprogram\dto\UserInfoResponse.class
com\tourism\miniprogram\mapper\UserMapper.class
com\tourism\miniprogram\mapper\ProvinceMapper.class
com\tourism\miniprogram\mapper\CouponMapper.class
com\tourism\miniprogram\entity\Product.class
com\tourism\miniprogram\service\ScenicService.class
com\tourism\miniprogram\entity\UsageRecord.class
com\tourism\miniprogram\controller\GuidePointController.class
com\tourism\miniprogram\config\FileUploadConfig.class
com\tourism\miniprogram\entity\User.class
