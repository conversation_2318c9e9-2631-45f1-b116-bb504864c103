# 旅游讲解小程序后端服务

## 项目简介

本项目是旅游讲解小程序的后端服务，基于Spring Boot 2.3.12开发，使用Java 1.8，提供微信小程序一键登录功能和完整的API文档。

## 技术栈

- **Java**: 1.8
- **Spring Boot**: 2.3.12.RELEASE
- **MyBatis Plus**: 3.4.3
- **MySQL**: 8.0+
- **Druid**: 数据库连接池
- **微信SDK**: weixin-java-miniapp 4.4.0
- **JWT**: 用户认证
- **Knife4j**: API文档 3.0.3
- **Hutool**: 工具类库
- **Lombok**: 简化代码

## 功能特性

- ✅ 微信小程序一键登录
- ✅ JWT用户认证
- ✅ 用户信息管理
- ✅ Knife4j API文档
- ✅ 全局异常处理
- ✅ 跨域支持
- ✅ 数据库连接池监控
- ✅ 日志记录

## 快速开始

### 1. 环境要求

- JDK 1.8+
- Maven 3.6+
- MySQL 8.0+

### 2. 数据库配置

1. 创建数据库：
```sql
CREATE DATABASE tourism_miniprogram DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

2. 执行初始化脚本：
```bash
mysql -u root -p tourism_miniprogram < src/main/resources/sql/init.sql
```

### 3. 配置文件

修改 `src/main/resources/application.yml` 中的配置：

```yaml
spring:
  datasource:
    url: ************************************************************************************************************************************************************
    username: your_username
    password: your_password

wechat:
  miniapp:
    app-id: your_wechat_app_id
    secret: your_wechat_secret
```

### 4. 启动项目

```bash
# 编译项目
mvn clean compile

# 启动项目
mvn spring-boot:run
```

### 5. 访问服务

- **API文档**: http://localhost:8080/api/doc.html
- **数据库监控**: http://localhost:8080/api/druid/index.html (admin/123456)
- **健康检查**: http://localhost:8080/api/auth/test

## API接口

### 认证相关

#### 微信小程序登录
```
POST /api/auth/wechat/login
```

请求参数：
```json
{
  "code": "微信授权码",
  "nickname": "用户昵称",
  "avatarUrl": "头像URL",
  "region": "用户地区",
  "phone": "手机号码"
}
```

响应结果：
```json
{
  "code": 200,
  "message": "登录成功",
  "data": {
    "userId": 1,
    "token": "JWT令牌",
    "nickname": "用户昵称",
    "avatarUrl": "头像URL",
    "region": "用户地区",
    "phone": "手机号码",
    "isNewUser": false
  },
  "timestamp": 1701234567890
}
```

### 用户相关

#### 获取用户信息
```
GET /api/user/{userId}
```

#### 更新用户信息
```
PUT /api/user/{userId}
```

## 数据库表结构

### users 用户表

| 字段名 | 类型 | 长度 | 是否为空 | 键 | 注释 |
|--------|------|------|----------|-----|------|
| user_id | int | 11 | 否 | 主键 | 用户ID |
| openid | varchar | 100 | 否 | 唯一 | 微信openid |
| nickname | varchar | 50 | 是 | | 用户昵称 |
| avatar_url | varchar | 200 | 是 | | 头像URL |
| region | varchar | 50 | 是 | | 用户地区 |
| created_at | datetime | | 否 | | 创建时间 |
| phone | varchar | 255 | 是 | | 手机号码 |
| deleted | tinyint | 1 | 否 | | 是否删除 |

## 项目结构

```
backend/
├── src/
│   ├── main/
│   │   ├── java/
│   │   │   └── com/tourism/miniprogram/
│   │   │       ├── MiniprogramBackendApplication.java  # 启动类
│   │   │       ├── common/                             # 通用类
│   │   │       │   └── Result.java                     # 响应结果类
│   │   │       ├── config/                             # 配置类
│   │   │       │   ├── CorsConfig.java                 # 跨域配置
│   │   │       │   ├── Knife4jConfig.java              # API文档配置
│   │   │       │   ├── MybatisPlusConfig.java          # MyBatis配置
│   │   │       │   └── WechatConfig.java               # 微信配置
│   │   │       ├── controller/                         # 控制器
│   │   │       │   ├── AuthController.java             # 认证控制器
│   │   │       │   └── UserController.java             # 用户控制器
│   │   │       ├── dto/                                # 数据传输对象
│   │   │       │   ├── LoginResponse.java              # 登录响应
│   │   │       │   └── WechatLoginRequest.java         # 微信登录请求
│   │   │       ├── entity/                             # 实体类
│   │   │       │   └── User.java                       # 用户实体
│   │   │       ├── exception/                          # 异常处理
│   │   │       │   └── GlobalExceptionHandler.java    # 全局异常处理器
│   │   │       ├── mapper/                             # 数据访问层
│   │   │       │   └── UserMapper.java                 # 用户Mapper
│   │   │       ├── service/                            # 服务层
│   │   │       │   ├── UserService.java                # 用户服务接口
│   │   │       │   ├── WechatService.java              # 微信服务
│   │   │       │   └── impl/                           # 服务实现
│   │   │       │       └── UserServiceImpl.java       # 用户服务实现
│   │   │       └── util/                               # 工具类
│   │   │           └── JwtUtil.java                    # JWT工具类
│   │   └── resources/
│   │       ├── application.yml                         # 配置文件
│   │       ├── mapper/                                 # MyBatis映射文件
│   │       │   └── UserMapper.xml                      # 用户Mapper XML
│   │       └── sql/                                    # SQL脚本
│   │           └── init.sql                            # 初始化脚本
│   └── test/                                           # 测试代码
├── pom.xml                                             # Maven配置
└── README.md                                           # 项目说明
```

## 开发说明

1. **代码规范**: 使用Lombok简化代码，统一使用驼峰命名
2. **异常处理**: 全局异常处理器统一处理异常
3. **日志记录**: 使用SLF4J记录关键操作日志
4. **API文档**: 使用Knife4j自动生成API文档
5. **数据校验**: 使用Validation进行参数校验

## 部署说明

1. 打包项目：`mvn clean package`
2. 运行jar包：`java -jar target/miniprogram-backend-1.0.0.jar`
3. 或使用Docker部署（需要编写Dockerfile）

## 注意事项

1. 请确保微信小程序的AppID和Secret配置正确
2. 数据库连接信息需要根据实际环境修改
3. JWT密钥建议在生产环境中使用更复杂的密钥
4. 建议在生产环境中关闭SQL日志输出

## 联系方式

如有问题，请联系开发团队。
