package com.tourism.miniprogram.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 讲解音频实体类
 *
 * <AUTHOR> Team
 * @since 2023-12-01
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("explanation_audio")
@ApiModel(value = "ExplanationAudio对象", description = "讲解音频信息")
public class ExplanationAudio implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "音频ID")
    @TableId(value = "audio_id", type = IdType.AUTO)
    private Integer audioId;

    @ApiModelProperty(value = "关联讲解点ID")
    @TableField("point_id")
    @NotNull(message = "讲解点ID不能为空")
    private Integer pointId;

    @ApiModelProperty(value = "音频名称")
    @TableField("audio_name")
    @NotBlank(message = "音频名称不能为空")
    private String audioName;

    @ApiModelProperty(value = "音频关联图片URL")
    @TableField("audio_image")
    private String audioImage;

    @ApiModelProperty(value = "音频时长(秒)")
    @TableField("duration")
    @NotNull(message = "音频时长不能为空")
    private Integer duration;

    @ApiModelProperty(value = "音频文件URL")
    @TableField("audio_url")
    @NotBlank(message = "音频文件URL不能为空")
    private String audioUrl;

    @ApiModelProperty(value = "音频顺序")
    @TableField("sort_order")
    private Integer sortOrder;

    @ApiModelProperty(value = "创建时间")
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    private LocalDateTime createdAt;

    @ApiModelProperty(value = "更新时间")
    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedAt;
}
