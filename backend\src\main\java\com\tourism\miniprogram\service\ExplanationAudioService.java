package com.tourism.miniprogram.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.tourism.miniprogram.entity.ExplanationAudio;

import java.util.List;

/**
 * 讲解音频服务接口
 *
 * <AUTHOR> Team
 * @since 2023-12-01
 */
public interface ExplanationAudioService extends IService<ExplanationAudio> {

    /**
     * 根据讲解点ID获取音频列表
     *
     * @param pointId 讲解点ID
     * @return 音频列表
     */
    List<ExplanationAudio> getAudiosByPointId(Integer pointId);
}
