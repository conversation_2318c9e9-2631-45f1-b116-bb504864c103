# 旅游讲解小程序 - 关系管理系统

## 概述

本系统实现了旅游讲解应用程序中的多对多关系管理功能，包括：

1. **产品 ↔ 区域** (多对多关系)
2. **区域 ↔ 讲解点** (多对多关系)  
3. **讲解点 ↔ 音频** (多对多关系)

## 系统架构

### 后端架构 (Java 1.8 + Spring Boot + MyBatis Plus)

采用标准的三层架构：

```
Controller层 (控制器)
    ↓
Service层 (业务逻辑)
    ↓
Mapper层 (数据访问)
```

### 数据库表结构

#### 基础表
- `explanation_area` - 讲解区域表
- `explanation_audio` - 讲解音频表

#### 关系表
- `product_area_relation` - 产品与区域关系表
- `area_point_relation` - 区域与讲解点关系表  
- `point_audio_relation` - 讲解点与音频关系表

### 前端架构 (Vue 3 + Element Plus)

- 统一的关系管理页面
- 扁平侧边栏导航结构
- 响应式设计和表单验证

## 功能特性

### 后端功能

#### 1. 产品-区域关系管理
- `GET /relations/product-area/product/{productId}` - 获取产品的区域关系
- `GET /relations/product-area/area/{areaId}` - 获取区域的产品关系
- `POST /relations/product-area` - 添加产品区域关系
- `DELETE /relations/product-area` - 删除产品区域关系
- `POST /relations/product-area/batch` - 批量添加关系
- `PUT /relations/product-area/{relationId}/sort` - 更新排序
- `PUT /relations/product-area/batch-sort` - 批量更新排序

#### 2. 区域-讲解点关系管理
- `GET /relations/area-point/area/{areaId}` - 获取区域的讲解点关系
- `GET /relations/area-point/point/{pointId}` - 获取讲解点的区域关系
- `POST /relations/area-point` - 添加区域讲解点关系
- `DELETE /relations/area-point` - 删除区域讲解点关系
- `POST /relations/area-point/batch` - 批量添加关系
- `PUT /relations/area-point/{relationId}/sort` - 更新排序

#### 3. 讲解点-音频关系管理
- `GET /relations/point-audio/point/{pointId}` - 获取讲解点的音频关系
- `GET /relations/point-audio/audio/{audioId}` - 获取音频的讲解点关系
- `POST /relations/point-audio` - 添加讲解点音频关系
- `DELETE /relations/point-audio` - 删除讲解点音频关系
- `POST /relations/point-audio/batch` - 批量添加关系
- `PUT /relations/point-audio/{relationId}/sort` - 更新排序

#### 4. 辅助查询接口
- `GET /relations/areas` - 获取所有区域列表
- `GET /relations/audios` - 获取所有音频列表

### 前端功能

#### 1. 统一管理界面
- 标签页切换不同关系类型
- 下拉选择器支持搜索和筛选
- 实时排序管理
- 批量操作支持

#### 2. 用户体验
- 响应式设计适配不同屏幕
- 表单验证和错误提示
- 加载状态和操作反馈
- 音频预览播放功能

## 安装和配置

### 1. 数据库初始化

执行SQL脚本创建表结构：

```sql
-- 运行 backend/src/main/resources/sql/relation_tables.sql
```

### 2. 后端配置

确保以下依赖已添加到 `pom.xml`：

```xml
<dependency>
    <groupId>com.baomidou</groupId>
    <artifactId>mybatis-plus-boot-starter</artifactId>
</dependency>
<dependency>
    <groupId>io.springfox</groupId>
    <artifactId>springfox-swagger2</artifactId>
</dependency>
```

### 3. 前端配置

确保已安装必要的依赖：

```bash
npm install vue@3 element-plus axios
```

## 使用说明

### 1. 访问管理界面

在浏览器中访问：`http://localhost:3000/relation-management`

### 2. 管理产品-区域关系

1. 选择"产品-区域关系"标签页
2. 从下拉列表中选择产品和区域
3. 点击"添加关系"按钮
4. 可以调整排序顺序
5. 支持批量操作

### 3. 管理区域-讲解点关系

1. 选择"区域-讲解点关系"标签页
2. 从下拉列表中选择区域和讲解点
3. 添加和管理关系
4. 支持排序调整

### 4. 管理讲解点-音频关系

1. 选择"讲解点-音频关系"标签页
2. 从下拉列表中选择讲解点和音频
3. 可以预览音频文件
4. 管理关系和排序

## API 安全性

- 所有关系管理API都是公开访问的，无需JWT认证
- 遵循RESTful API设计规范
- 支持CORS跨域访问

## 错误处理

### 后端错误处理
- 全局异常处理器捕获所有异常
- 参数验证和业务逻辑验证
- 详细的错误日志记录

### 前端错误处理
- 网络请求错误提示
- 表单验证错误显示
- 用户友好的错误消息

## 性能优化

### 后端优化
- 数据库索引优化
- 批量操作支持
- 事务管理

### 前端优化
- 组件懒加载
- 数据缓存机制
- 防抖和节流

## 扩展性

系统设计支持以下扩展：

1. **新增关系类型** - 可以轻松添加新的多对多关系
2. **权限控制** - 可以集成用户权限管理
3. **审计日志** - 可以添加操作日志记录
4. **数据导入导出** - 支持Excel等格式的数据交换

## 技术栈

### 后端
- Java 1.8
- Spring Boot 2.x
- MyBatis Plus
- MySQL 8.0
- Swagger/Knife4j

### 前端  
- Vue 3
- Element Plus
- Vue Router
- Axios
- Vite

## 联系方式

如有问题或建议，请联系开发团队。
