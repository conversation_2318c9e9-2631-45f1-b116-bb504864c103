<template>
  <div class="sidebar-wrapper">
    <div class="sidebar-logo">
      <router-link to="/" class="sidebar-logo-link">
        <img src="/logo.png" alt="logo" class="sidebar-logo-img" />
        <h1 v-show="!isCollapse" class="sidebar-title">旅游管理后台</h1>
      </router-link>
    </div>
    
    <el-scrollbar wrap-class="scrollbar-wrapper">
      <el-menu
        :default-active="activeMenu"
        :collapse="isCollapse"
        :unique-opened="false"
        :collapse-transition="false"
        mode="vertical"
        background-color="#304156"
        text-color="#bfcbd9"
        active-text-color="#409EFF"
      >
        <!-- 直接渲染菜单项，不使用子组件 -->
        <template v-for="route in routes" :key="route.path">
          <router-link :to="route.path" custom v-slot="{ navigate }">
            <el-menu-item :index="route.path" @click="navigate">
              <el-icon v-if="route.meta && route.meta.icon">
                <component :is="route.meta.icon" />
              </el-icon>
              <template #title>
                <span>{{ route.meta.title }}</span>
              </template>
            </el-menu-item>
          </router-link>
        </template>
      </el-menu>
    </el-scrollbar>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { useRoute } from 'vue-router'
import { useAppStore } from '@/stores/app'

const route = useRoute()
const appStore = useAppStore()

const isCollapse = computed(() => !appStore.sidebar.opened)
const activeMenu = computed(() => route.path)

// 侧边栏菜单配置 - 所有菜单项都作为一级菜单
const routes = [
  {
    path: '/dashboard',
    name: 'Dashboard',
    meta: { title: '仪表盘', icon: 'House' }
  },
  {
    path: '/user/list',
    name: 'UserList',
    meta: { title: '用户管理', icon: 'User' }
  },
  {
    path: '/province/list',
    name: 'ProvinceList',
    meta: { title: '省份管理', icon: 'Location' }
  },
  {
    path: '/city/list',
    name: 'CityList',
    meta: { title: '城市管理', icon: 'OfficeBuilding' }
  },
  {
    path: '/scenic/list',
    name: 'ScenicList',
    meta: { title: '景区管理', icon: 'MapLocation' }
  },
  {
    path: '/carousel/list',
    name: 'CarouselList',
    meta: { title: '轮播图管理', icon: 'Picture' }
  },
  {
    path: '/shouye-carousel',
    name: 'ShouyeCarousel',
    meta: { title: '首页轮播图管理', icon: 'Monitor' }
  },
  {
    path: '/review/list',
    name: 'ReviewList',
    meta: { title: '评价管理', icon: 'ChatDotRound' }
  },
  {
    path: '/guide-product/list',
    name: 'GuideProductList',
    meta: { title: '讲解产品管理', icon: 'Headset' }
  },
  {
    path: '/explanation-area/list',
    name: 'ExplanationAreaList',
    meta: { title: '讲解区域管理', icon: 'Grid' }
  },
  {
    path: '/explanation-point/list',
    name: 'ExplanationPointList',
    meta: { title: '讲解点管理', icon: 'LocationInformation' }
  },
  {
    path: '/explanation-audio/list',
    name: 'ExplanationAudioList',
    meta: { title: '讲解音频管理', icon: 'Microphone' }
  },
  {
    path: '/lecturer',
    name: 'LecturerList',
    meta: { title: '讲师管理', icon: 'Avatar' }
  },
  {
    path: '/product',
    name: 'ProductList',
    meta: { title: '产品管理', icon: 'Box' }
  },
  {
    path: '/product-bundle',
    name: 'ProductBundleList',
    meta: { title: '组合包管理', icon: 'Collection' }
  },
  {
    path: '/order',
    name: 'OrderList',
    meta: { title: '订单管理', icon: 'Document' }
  },
  {
    path: '/coupon',
    name: 'CouponList',
    meta: { title: '门票管理', icon: 'Ticket' }
  },
  {
    path: '/activation-code',
    name: 'ActivationCodeList',
    meta: { title: '激活码管理', icon: 'Key' }
  },
  {
    path: '/usage-record',
    name: 'UsageRecordList',
    meta: { title: '使用记录', icon: 'List' }
  }
]


</script>

<style lang="scss" scoped>
.sidebar-wrapper {
  height: 100%;
  background-color: #304156;
  width: 210px !important;
  position: fixed;
  font-size: 0px;
  top: 0;
  bottom: 0;
  left: 0;
  z-index: 1001;
  overflow: hidden;
  transition: width 0.28s;
  
  .sidebar-logo {
    width: 100%;
    height: 50px;
    line-height: 50px;
    background: #2b2f3a;
    text-align: center;
    overflow: hidden;
    
    .sidebar-logo-link {
      height: 100%;
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      text-decoration: none;
      
      .sidebar-logo-img {
        width: 32px;
        height: 32px;
        vertical-align: middle;
        margin-right: 12px;
      }
      
      .sidebar-title {
        display: inline-block;
        margin: 0;
        color: #fff;
        font-weight: 600;
        line-height: 50px;
        font-size: 14px;
        font-family: Avenir, Helvetica Neue, Arial, Helvetica, sans-serif;
        vertical-align: middle;
      }
    }
  }
  
  .el-menu {
    border: none;
    height: 100%;
    width: 100% !important;
  }
}

.hideSidebar .sidebar-wrapper {
  width: 54px !important;
}

.mobile .sidebar-wrapper {
  transition: transform 0.28s;
  width: 210px !important;
}

.mobile.hideSidebar .sidebar-wrapper {
  pointer-events: none;
  transition-duration: 0.3s;
  transform: translate3d(-210px, 0, 0);
}
</style>
